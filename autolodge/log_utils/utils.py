"""
Logging utilities and setup functions.

This module provides shared logging utilities including the main setup_logging function
that configures both Azure Blob Storage and local file logging with proper fallback mechanisms.
"""

from datetime import datetime
from pathlib import Path
from typing import Union

from loguru import logger

from .azure_sink import AzureBlobStorageSink
from .config import AzureLoggingConfig, check_azure_dependencies


def setup_logging() -> str:
    """
    Configure Loguru logging with Azure Blob Storage and terminal output.

    This function sets up a dual logging architecture:
    1. Terminal/console output with colored formatting (unchanged)
    2. Remote Azure Blob Storage logging (if configured) or local file logging (fallback)

    The function automatically detects Azure configuration and falls back to local
    file logging if Azure is not available or configured.

    Returns:
        str: Path to log destination (Azure blob URI or local file path)
    """
    # Remove default handler
    logger.remove()

    # Add console handler with colored output (unchanged)
    logger.add(
        sink=lambda msg: print(msg, end=''),
        format='<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
        level='INFO',
        colorize=True,
    )

    # Load Azure configuration
    azure_config = AzureLoggingConfig()
    azure_config.log_configuration_status()

    # Check if Azure dependencies are available
    azure_available = check_azure_dependencies()

    # Try to set up Azure Blob Storage logging
    azure_sink_configured = False
    if azure_available and azure_config.is_configured():
        try:
            # Create Azure Blob Storage sink with environment-specific naming
            azure_sink = AzureBlobStorageSink(
                connection_string=azure_config.get_connection_string(),
                container_name=azure_config.container_name,
                blob_prefix=azure_config.blob_prefix,
                environment=azure_config.environment,
            )

            # Add Azure Blob Storage handler
            logger.add(
                sink=azure_sink.write,
                format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
                level='DEBUG',
                enqueue=True,  # Thread-safe logging
                backtrace=True,  # Include traceback in error logs
                diagnose=True,  # Include variable values in traceback
            )

            azure_sink_configured = True
            logger.info('✅ Azure Blob Storage logging configured successfully')
            return f'azure://{azure_config.container_name}/{azure_sink.current_blob_name}'

        except Exception as e:
            logger.error(f'❌ Failed to configure Azure Blob Storage logging: {str(e)}')
            logger.warning('⚠️ Falling back to local file logging')

    # Fallback to local file logging if Azure is not available or configured
    if not azure_sink_configured:
        return _setup_local_file_logging(azure_available, azure_config)


def _setup_local_file_logging(azure_available: bool, azure_config: AzureLoggingConfig) -> str:
    """
    Set up local file logging as fallback when Azure is not available or configured.

    Args:
        azure_available: Whether Azure dependencies are available
        azure_config: Azure configuration object

    Returns:
        str: Path to the local log file
    """
    # Log why we're falling back to local logging
    if not azure_available:
        logger.warning('⚠️ Azure Blob Storage dependencies not available')
    elif not azure_config.is_configured():
        azure_config.log_configuration_hints()

    # Create logs directory if it doesn't exist
    # Use the parent directory of this module to find the autolodge package root
    autolodge_root = Path(__file__).parent.parent
    logs_dir = autolodge_root / 'logs'
    logs_dir.mkdir(exist_ok=True)

    # Generate environment-specific timestamp for log filename
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    log_file = logs_dir / f'score_{azure_config.environment}_{timestamp}.log'

    # Add local file handler with rotation
    logger.add(
        sink=str(log_file),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='1 day',  # Rotate daily
        retention='30 days',  # Keep logs for 30 days
        compression='zip',  # Compress old logs
        enqueue=True,  # Thread-safe logging
        backtrace=True,  # Include traceback in error logs
        diagnose=True,  # Include variable values in traceback
    )

    # Add size-based rotation as backup
    logger.add(
        sink=str(logs_dir / f'score_{azure_config.environment}_size_rotated_{timestamp}.log'),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='10 MB',  # Rotate when file reaches 10MB
        retention=10,  # Keep 10 rotated files
        compression='zip',
        enqueue=True,
    )

    logger.info(f'📁 Local file logging configured - Log file: {log_file}')
    return str(log_file)


def add_console_handler() -> None:
    """
    Add a console handler to the logger with colored output.

    This is a utility function that can be used to add console logging
    to other modules that need it.
    """
    logger.add(
        sink=lambda msg: print(msg, end=''),
        format='<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
        level='INFO',
        colorize=True,
    )


def add_file_handler(file_path: Union[str, Path], level: str = 'DEBUG') -> None:
    """
    Add a file handler to the logger.

    Args:
        file_path: Path to the log file
        level: Logging level (default: 'DEBUG')
    """
    logger.add(
        sink=str(file_path),
        format='{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}',
        level=level,
        rotation='1 day',
        retention='30 days',
        compression='zip',
        enqueue=True,
        backtrace=True,
        diagnose=True,
    )
