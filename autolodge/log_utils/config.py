"""
Azure Blob Storage logging configuration module.

This module handles environment variable loading and configuration for Azure Blob Storage logging.
It provides a centralized way to manage Azure logging settings with proper validation and defaults.
"""

import os
from dataclasses import dataclass
from typing import Optional

from dotenv import load_dotenv
from loguru import logger

# Load environment variables
load_dotenv()


@dataclass
class AzureLoggingConfig:
    """
    Configuration class for Azure Blob Storage logging.

    This class loads and validates Azure logging configuration from environment variables,
    providing sensible defaults and proper error handling.
    """

    connection_string: Optional[str] = None
    storage_account: Optional[str] = None
    storage_key: Optional[str] = None
    container_name: str = 'autolodge-logs'
    blob_prefix: str = 'score'

    def __post_init__(self):
        """Load configuration from environment variables after initialization."""
        self._load_from_environment()

    def _load_from_environment(self) -> None:
        """Load Azure configuration from environment variables."""
        # Load Azure Blob Storage configuration from environment variables
        self.connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
        self.storage_account = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
        self.storage_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')
        self.container_name = os.getenv('AZURE_LOG_CONTAINER_NAME', 'autolodge-logs')
        self.blob_prefix = os.getenv('AZURE_LOG_BLOB_PREFIX', 'score')

    def get_connection_string(self) -> Optional[str]:
        """
        Get the Azure Storage connection string.

        Returns the connection string from environment variables, or constructs one
        from storage account name and key if available.

        Returns:
            Optional[str]: Connection string if available, None otherwise
        """
        if self.connection_string:
            return self.connection_string
        elif self.storage_account and self.storage_key:
            # Construct connection string from account name and key
            return f'DefaultEndpointsProtocol=https;AccountName={self.storage_account};AccountKey={self.storage_key};EndpointSuffix=core.windows.net'
        return None

    def is_configured(self) -> bool:
        """
        Check if Azure Blob Storage logging is properly configured.

        Returns:
            bool: True if Azure configuration is available, False otherwise
        """
        return self.get_connection_string() is not None

    def log_configuration_status(self) -> None:
        """Log the current configuration status with emoji indicators."""
        # Log environment variable loading
        if self.connection_string:
            logger.info('🔧 Loaded Azure Storage connection string from environment variables')
        elif self.storage_account and self.storage_key:
            logger.info('🔧 Loaded Azure Storage account credentials from environment variables')

        logger.info(f'🔧 Azure Blob Storage container: {self.container_name}')
        logger.info(f'🔧 Azure Blob Storage prefix: {self.blob_prefix}')

    def log_configuration_hints(self) -> None:
        """Log helpful configuration hints when Azure is not configured."""
        if not self.is_configured():
            logger.warning('⚠️ Azure Storage connection string not configured')
            logger.info(
                '💡 Set AZURE_STORAGE_CONNECTION_STRING or (AZURE_STORAGE_ACCOUNT_NAME + AZURE_STORAGE_ACCOUNT_KEY) environment variables to enable Azure Blob Storage logging'
            )


def get_azure_logging_config() -> AzureLoggingConfig:
    """
    Get Azure logging configuration from environment variables.

    Returns:
        AzureLoggingConfig: Configuration object with Azure settings
    """
    return AzureLoggingConfig()


def check_azure_dependencies() -> bool:
    """
    Check if Azure Blob Storage dependencies are available.

    Returns:
        bool: True if Azure dependencies are available, False otherwise
    """
    try:
        import threading
        import time

        from azure.storage.blob import BlobServiceClient

        return True
    except ImportError:
        return False
